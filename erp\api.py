"""
API decorators for ERP models - Similar to Odoo's @api decorators

This module provides decorators for model methods to handle field dependencies,
caching, and other model-related functionality.
"""

import functools
import logging
from typing import Callable, List

logger = logging.getLogger(__name__)


def depends(*field_names: str) -> Callable:
    """
    Decorator to specify field dependencies for compute methods.
    
    This decorator is used to mark which fields a compute method depends on.
    When any of the specified fields change, the compute method will be
    automatically triggered to recalculate the computed field value.
    
    Args:
        *field_names: Names of fields that this compute method depends on.
                     Can include dot notation for related fields (e.g., 'partner_id.name')
    
    Returns:
        Decorated function with dependency metadata
        
    Example:
        class Partner(BaseModel):
            _name = 'res.partner'
            
            name = fields.Char(string='Name')
            is_company = fields.Boolean(string='Is Company')
            company_type = fields.Selection([
                ('person', 'Individual'),
                ('company', 'Company')
            ], compute='_compute_company_type')
            
            @depends('is_company')
            def _compute_company_type(self):
                for record in self:
                    record.company_type = 'company' if record.is_company else 'person'
    """
    def decorator(func: Callable) -> Callable:
        # Store dependency information on the function
        if not hasattr(func, '_depends'):
            func._depends = []
        func._depends.extend(field_names)
        
        # Remove duplicates while preserving order
        func._depends = list(dict.fromkeys(func._depends))
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # Copy dependency metadata to wrapper
        wrapper._depends = func._depends
        
        logger.debug(f"Registered dependencies for {func.__name__}: {func._depends}")
        return wrapper
    
    return decorator


def constrains(*field_names: str) -> Callable:
    """
    Decorator to specify field constraints.
    
    This decorator is used to mark which fields trigger constraint validation.
    When any of the specified fields change, the constraint method will be
    automatically called to validate the record.
    
    Args:
        *field_names: Names of fields that trigger this constraint
    
    Returns:
        Decorated function with constraint metadata
        
    Example:
        class Partner(BaseModel):
            _name = 'res.partner'
            
            email = fields.Char(string='Email')
            
            @constrains('email')
            def _check_email_format(self):
                for record in self:
                    if record.email and '@' not in record.email:
                        raise ValidationError("Invalid email format")
    """
    def decorator(func: Callable) -> Callable:
        # Store constraint information on the function
        if not hasattr(func, '_constrains'):
            func._constrains = []
        func._constrains.extend(field_names)
        
        # Remove duplicates while preserving order
        func._constrains = list(dict.fromkeys(func._constrains))
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # Copy constraint metadata to wrapper
        wrapper._constrains = func._constrains
        
        logger.debug(f"Registered constraints for {func.__name__}: {func._constrains}")
        return wrapper
    
    return decorator


def onchange(*field_names: str) -> Callable:
    """
    Decorator to specify onchange methods.
    
    This decorator is used to mark which fields trigger onchange methods.
    When any of the specified fields change in the UI, the onchange method
    will be called to update other fields or provide warnings.
    
    Args:
        *field_names: Names of fields that trigger this onchange
    
    Returns:
        Decorated function with onchange metadata
        
    Example:
        class Partner(BaseModel):
            _name = 'res.partner'
            
            country_id = fields.Many2One('res.country', string='Country')
            state_id = fields.Many2One('res.country.state', string='State')
            
            @onchange('country_id')
            def _onchange_country_id(self):
                if self.country_id:
                    self.state_id = False  # Clear state when country changes
    """
    def decorator(func: Callable) -> Callable:
        # Store onchange information on the function
        if not hasattr(func, '_onchange'):
            func._onchange = []
        func._onchange.extend(field_names)
        
        # Remove duplicates while preserving order
        func._onchange = list(dict.fromkeys(func._onchange))
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # Copy onchange metadata to wrapper
        wrapper._onchange = func._onchange
        
        logger.debug(f"Registered onchange for {func.__name__}: {func._onchange}")
        return wrapper
    
    return decorator


def model(func: Callable) -> Callable:
    """
    Decorator for model-level methods.
    
    This decorator is used to mark methods that operate on the model class
    rather than on record instances. These methods don't have access to
    record data and are typically used for utility functions.
    
    Args:
        func: The function to decorate
    
    Returns:
        Decorated function with model metadata
        
    Example:
        class Partner(BaseModel):
            _name = 'res.partner'
            
            @model
            def get_default_country(self):
                # This method operates on the model, not on records
                return self.env['res.country'].search([('code', '=', 'US')], limit=1)
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    
    # Mark as model method
    wrapper._model_method = True
    
    logger.debug(f"Registered model method: {func.__name__}")
    return wrapper


def multi(func: Callable) -> Callable:
    """
    Decorator for methods that work on multiple records.
    
    This decorator is used to mark methods that are designed to work
    on recordsets (multiple records at once). This is the default
    behavior in modern Odoo, but this decorator can be used for clarity.
    
    Args:
        func: The function to decorate
    
    Returns:
        Decorated function with multi metadata
        
    Example:
        class Partner(BaseModel):
            _name = 'res.partner'
            
            @multi
            def archive_partners(self):
                # This method works on multiple records
                self.write({'active': False})
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    
    # Mark as multi method
    wrapper._multi_method = True
    
    logger.debug(f"Registered multi method: {func.__name__}")
    return wrapper





# Convenience function to get dependency information from a method
def get_depends(method: Callable) -> List[str]:
    """
    Get the list of field dependencies for a method.
    
    Args:
        method: The method to check for dependencies
    
    Returns:
        List of field names that the method depends on
    """
    return getattr(method, '_depends', [])


# Convenience function to get constraint information from a method
def get_constrains(method: Callable) -> List[str]:
    """
    Get the list of field constraints for a method.
    
    Args:
        method: The method to check for constraints
    
    Returns:
        List of field names that trigger this constraint
    """
    return getattr(method, '_constrains', [])


# Convenience function to get onchange information from a method
def get_onchange(method: Callable) -> List[str]:
    """
    Get the list of onchange fields for a method.
    
    Args:
        method: The method to check for onchange
    
    Returns:
        List of field names that trigger this onchange
    """
    return getattr(method, '_onchange', [])
