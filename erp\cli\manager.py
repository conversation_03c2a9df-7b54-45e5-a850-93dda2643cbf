"""
ERP CLI Manager - Main command line interface coordinator
"""
import sys
import argparse
from typing import Dict, Optional

from .base import CommandGroup
from .database import DatabaseCommandGroup
from .server import ServerCommandGroup
from .utils import (
    setup_python_path, setup_logging, print_system_info,
    handle_keyboard_interrupt, handle_unexpected_error
)
from ..config import config


class ERPCLIManager:
    """Main ERP CLI Manager that coordinates all command groups"""

    def __init__(self):
        self.command_groups: Dict[str, CommandGroup] = {}
        self.parser = self._create_parser()
        self._register_command_groups()

    def _create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser"""
        parser = argparse.ArgumentParser(
            description='ERP System Command Line Interface',
            prog='erp-bin',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  erp-bin init mydb                    # Initialize database
  erp-bin start --db mydb              # Start server with database
  erp-bin start                        # Start server in multi-database mode
  erp-bin purge                        # Remove all databases

Legacy compatibility:
  erp-bin mydb --force                 # Same as: erp-bin init mydb --force
  erp-bin --db mydb                    # Same as: erp-bin start --db mydb
            """
        )

        # Global options
        parser.add_argument('--config', '-c', help='Configuration file path')
        parser.add_argument('--quiet', action='store_true', help='Suppress banner output')
        parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
        parser.add_argument('--version', action='version', version='ERP System 1.0.0')

        return parser

    def _register_command_groups(self):
        """Register all command groups"""
        # Register command groups
        self.command_groups['database'] = DatabaseCommandGroup()
        self.command_groups['server'] = ServerCommandGroup()

        # Create a parent parser with global options that will be inherited by subcommands
        parent_parser = argparse.ArgumentParser(add_help=False)
        parent_parser.add_argument('--quiet', action='store_true', help='Suppress banner output')
        parent_parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

        # Add subcommands to parser
        subparsers = self.parser.add_subparsers(
            dest='command',
            help='Available commands',
            metavar='COMMAND'
        )

        # Add commands from each group with parent parser
        for group_name, group in self.command_groups.items():
            group.add_commands(subparsers, parent_parser)

    def run(self, args: Optional[list] = None) -> int:
        """Run the CLI with given arguments"""
        try:
            # Setup Python path
            setup_python_path()

            # Parse arguments
            parsed_args = self.parser.parse_args(args)

            # Setup logging
            setup_logging(getattr(parsed_args, 'quiet', False))

            # Show system info unless quiet mode
            if not getattr(parsed_args, 'quiet', False):
                db_name = getattr(parsed_args, 'db_name', None)
                print_system_info(db_name)

            # Apply global configuration
            self._apply_global_config(parsed_args)

            if not parsed_args.command:
                self.parser.print_help()
                return 1

            # Route command to appropriate handler
            return self._handle_command(parsed_args)

        except KeyboardInterrupt:
            handle_keyboard_interrupt()
            return 130
        except Exception as e:
            verbose = getattr(parsed_args, 'verbose', False) if 'parsed_args' in locals() else False
            handle_unexpected_error(e, verbose)
            return 1

    def _apply_global_config(self, args: argparse.Namespace):
        """Apply global configuration options"""
        if hasattr(args, 'config') and args.config:
            config.config_file = args.config
            config._load_config()

        if hasattr(args, 'db_name') and args.db_name:
            config.set('options', 'db_name', args.db_name)

        if hasattr(args, 'addons_path') and args.addons_path:
            config.set('options', 'addons_path', args.addons_path)

    def _handle_command(self, args: argparse.Namespace) -> int:
        """Handle command execution by routing to appropriate command group"""
        command_name = args.command

        # Map commands to their respective groups
        command_mapping = {
            # Database commands
            'init': 'database',
            'purge': 'database',
            # Server commands
            'start': 'server',
        }

        group_name = command_mapping.get(command_name)
        if not group_name:
            print(f"Unknown command: {command_name}")
            return 1

        command_group = self.command_groups.get(group_name)
        if not command_group:
            print(f"Command group not found: {group_name}")
            return 1

        # Map command names to their handler names in the group
        command_handler_mapping = {
            # Database commands
            'init': 'init',
            'purge': 'purge',
            # Server commands
            'start': 'start',
        }

        handler_name = command_handler_mapping.get(command_name, command_name)
        return command_group.handle_command(handler_name, args)

    def print_command_help(self, _command_name: str):
        """Print help for a specific command"""
        # This could be enhanced to show detailed help for specific commands
        self.parser.print_help()

    def list_commands(self) -> Dict[str, list]:
        """List all available commands grouped by category"""
        commands = {}
        for group_name, group in self.command_groups.items():
            commands[group_name] = list(group.commands.keys())
        return commands

    def get_command_info(self, command_name: str) -> Optional[dict]:
        """Get information about a specific command"""
        for group_name, group in self.command_groups.items():
            if command_name in group.commands:
                command = group.commands[command_name]
                return {
                    'name': command_name,
                    'group': group_name,
                    'class': command.__class__.__name__,
                    'description': command.__class__.__doc__ or 'No description available'
                }
        return None


def main():
    """Main entry point for the CLI"""
    manager = ERPCLIManager()
    return manager.run()


if __name__ == '__main__':
    sys.exit(main())