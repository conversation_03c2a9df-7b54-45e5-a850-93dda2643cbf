"""
Common response utilities to eliminate duplicate code patterns
"""
from typing import Any, Dict
from fastapi import HTTPException





class ModelResponse:
    """Model-specific response utilities"""

    @staticmethod
    def model_not_found(model_name: str) -> HTTPException:
        """Model not found error"""
        raise HTTPException(status_code=404, detail={"error": f"Model '{model_name}' not found"})

    @staticmethod
    def method_not_found(model_name: str, method: str) -> HTTPException:
        """Method not found error"""
        raise HTTPException(
            status_code=404,
            detail={"error": f"Method '{method}' not found on model '{model_name}'"}
        )

    @staticmethod
    def model_result(result: Any) -> Dict[str, Any]:
        """Wrap model operation result"""
        return {"result": result}

    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create success response for model operations"""
        response = {"success": True, "message": message}
        if data is not None:
            response["data"] = data
        return response


def handle_database_error(e: Exception) -> HTTPException:
    """Handle database errors consistently"""
    error_msg = str(e)

    # Map specific database errors to appropriate HTTP status codes
    if "does not exist" in error_msg.lower():
        raise HTTPException(status_code=404, detail={"error": "Resource not found"})
    if "duplicate key" in error_msg.lower():
        raise HTTPException(status_code=409, detail={"error": "Resource already exists"})
    if "permission denied" in error_msg.lower():
        raise HTTPException(status_code=403, detail={"error": "Access denied"})

    raise HTTPException(status_code=500, detail={"error": f"Database error: {error_msg}"})


def handle_generic_error(e: Exception) -> HTTPException:
    """Handle generic errors consistently"""
    raise HTTPException(status_code=500, detail={"error": str(e)})
