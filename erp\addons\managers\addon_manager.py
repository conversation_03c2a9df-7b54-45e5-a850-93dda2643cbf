"""
Main addon manager

This module provides the main AddonManager class that coordinates
addon discovery, installation, and lifecycle management.
"""
import os
import importlib
from typing import Dict, List, Optional, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from ...environment import Environment

from .addon_state import <PERSON>donState, AddonInfo, AddonStateManager
from .dependency_manager import DependencyManager, InstallationPlan
from .savepoint_manager import SavepointManager
from ..manifest import AddonManifest
from ...config import config
from ...environment import EnvironmentManager
from ...logging import get_logger

logger = get_logger(__name__)


class AddonManager:
    """Enhanced addon manager with lifecycle management"""

    def __init__(self, addons_path: str = None):
        self.logger = get_logger(__name__)
        if addons_path:
            # Single path provided - convert to list for compatibility
            self.addons_paths = [addons_path]
            self.addons_path = addons_path
        else:
            # Use configured paths
            self.addons_paths = config.addons_paths
            self.addons_path = config.addons_path
        
        # Initialize managers
        self.state_manager = AddonStateManager()
        self.dependency_manager = DependencyManager(self.state_manager)
        self.savepoint_manager = SavepointManager()

        # Use installer for addon management
        from ..installers import AddonInstaller, BaseModuleInstaller
        self.installer = AddonInstaller()
        self.base_installer = BaseModuleInstaller()
    
    async def discover_addons(self) -> Dict[str, AddonInfo]:
        """Discover all available addons and update states"""
        discovered = {}

        # Discover addons from all configured paths
        for addons_path in self.addons_paths:
            if not os.path.exists(addons_path):
                continue

            for item in os.listdir(addons_path):
                addon_path = os.path.join(addons_path, item)

                # Skip if not a directory
                if not os.path.isdir(addon_path):
                    continue

                # Skip hidden directories and __pycache__
                if item.startswith('.') or item == '__pycache__':
                    continue

                # Check if it has a manifest file
                manifest_file = os.path.join(addon_path, '__manifest__.py')
                if os.path.exists(manifest_file):
                    manifest = AddonManifest(addon_path)
                    discovered[item] = manifest

                    # Register addon path for import system
                    from .. import _addon_import_manager
                    _addon_import_manager.register_addon_path(item, addon_path)

                    # Don't automatically import addons during discovery to avoid
                    # triggering route registration during database initialization.
                    # Addons will be imported when they are actually installed.
                    self.logger.debug(f"Registered addon path for {item}")

        # Update addon states with discovered addons
        for name, manifest in discovered.items():
            existing_info = self.state_manager.get_addon_info(name)
            if not existing_info:
                # New addon discovered
                addon_info = AddonInfo(
                    name=name,
                    manifest=manifest,
                    state=AddonState.UNINSTALLED,
                    available_version=manifest.version
                )
                self.state_manager.set_addon_info(name, addon_info)
            else:
                # Update existing addon info
                existing_info.manifest = manifest
                existing_info.available_version = manifest.version
                
                # Check if upgrade is needed
                if self.state_manager.check_upgrade_needed(name):
                    existing_info.state = AddonState.TO_UPGRADE
        
        # Build dependency graphs
        self.dependency_manager.build_dependency_graphs()
        
        return self.state_manager.get_all_states()
    
    async def install_addon(self, addon_name: str, force: bool = False, env: 'Environment' = None) -> bool:
        """Install an addon and its dependencies using enhanced dependency-aware savepoint pattern."""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            raise ValueError(f"Addon {addon_name} not found")

        # Check if already installed
        if addon_info.state == AddonState.INSTALLED and not force:
            self.logger.debug(f"Addon {addon_name} is already installed")
            return True

        # Check if installable
        if not addon_info.manifest.installable:
            raise ValueError(f"Addon {addon_name} is not installable")

        # Create environment if not provided
        if env is None:
            db_name = config.get_default_database()
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user

        # Get enhanced installation plan - let exceptions propagate
        installation_plan = self.dependency_manager.get_enhanced_install_order([addon_name])

        # Validate installation order
        is_valid, violations = self.dependency_manager.validate_installation_order(installation_plan.installation_order)
        if not is_valid:
            raise ValueError(f"Invalid installation order: {violations}")

        self.logger.info(f"Installing addons: {' -> '.join(installation_plan.installation_order)}")
        self.logger.info(f"Dependencies: {', '.join(installation_plan.dependency_addons)}")
        self.logger.info(f"Explicit addons: {', '.join(installation_plan.explicit_addons)}")

        # Start lifecycle tracking session
        from .lifecycle_tracker import get_lifecycle_tracker
        from .registry_refresh_coordinator import get_refresh_coordinator

        lifecycle_tracker = get_lifecycle_tracker()
        refresh_coordinator = get_refresh_coordinator()

        session_id = await lifecycle_tracker.start_installation_session(
            env.cr.db_name,
            list(installation_plan.explicit_addons),
            installation_plan.installation_order
        )

        # Register completion callback for registry refresh
        await lifecycle_tracker.add_completion_callback(
            session_id,
            refresh_coordinator.execute_session_completion_refresh
        )

        # Set session ID in savepoint manager
        self.savepoint_manager.set_session_id(session_id)

        try:
            # Install addons using dependency-aware savepoint strategy
            result = await self._install_with_dependency_savepoints(installation_plan, env, force)

            # Clean up session after completion
            await lifecycle_tracker.cleanup_session(session_id)

            return result
        except Exception:
            # Clean up session on error
            await lifecycle_tracker.cleanup_session(session_id)
            raise


    async def _install_with_dependency_savepoints(self, installation_plan: InstallationPlan,
                                                 env: 'Environment', force: bool = False) -> bool:
        """
        Install addons using dependency-aware savepoint strategy.

        Each addon gets its own savepoint. Dependencies are committed immediately
        upon successful installation, while main addons are only committed at the end.
        This ensures dependencies remain installed even if main addons fail.
        """
        successful_installs = []
        failed_addon = None

        for current_addon in installation_plan.installation_order:
            current_info = self.state_manager.get_addon_info(current_addon)

            # Skip if already installed (unless force)
            if not force and current_info and current_info.state == AddonState.INSTALLED:
                self.logger.debug(f"Addon '{current_addon}' already installed, skipping")
                successful_installs.append(current_addon)
                continue

            # Determine if this is a dependency or explicit addon
            is_dependency = installation_plan.is_dependency(current_addon)

            # Install addon with savepoint
            async def install_current_addon():
                if not current_info:
                    raise Exception(f"Addon '{current_addon}' not found in states")

                # Handle base module installation with dedicated installer
                if current_addon == 'base':
                    self.logger.debug("Using BaseModuleInstaller for base module")
                    success = await self.base_installer.install_base_module(env)
                    if not success:
                        raise Exception("Base module installation failed")
                else:
                    # Import the addon to register its hooks before installation
                    try:
                        module_name = f'erp.addons.{current_addon}'
                        importlib.import_module(module_name)
                        self.logger.debug(f"Imported addon module: {module_name}")
                    except ImportError as e:
                        self.logger.debug(f"Could not import addon {current_addon}: {e}")
                        # Continue anyway - some addons might not have Python modules

                    # Install the addon (execute install hooks)
                    success = await self.installer.install_addon(current_addon, env)
                    if not success:
                        raise Exception(f"Installation hooks failed for addon: {current_addon}")

                # Update state with dependency tracking
                self.state_manager.mark_addon_installed(
                    current_addon,
                    current_info.available_version,
                    as_dependency=is_dependency
                )
                return True

            # Execute installation with savepoint
            success = await self.savepoint_manager.execute_with_savepoint(
                current_addon, env, install_current_addon, is_dependency
            )

            if success:
                successful_installs.append(current_addon)

                # Notify lifecycle tracker of addon completion
                from .lifecycle_tracker import get_lifecycle_tracker
                lifecycle_tracker = get_lifecycle_tracker()
                await lifecycle_tracker.mark_addon_completed(current_addon, True)
            else:
                # Mark addon as broken
                self.state_manager.mark_addon_broken(current_addon)
                failed_addon = current_addon

                # Notify lifecycle tracker of addon failure
                from .lifecycle_tracker import get_lifecycle_tracker
                lifecycle_tracker = get_lifecycle_tracker()
                await lifecycle_tracker.mark_addon_completed(current_addon, False)
                break

        # Report results
        summary = self.savepoint_manager.get_installation_summary()

        if successful_installs:
            self.logger.info(f"Successfully installed addons: {', '.join(successful_installs)}")

        if failed_addon:
            self.logger.error(f"Installation stopped at addon '{failed_addon}'. "
                            f"Dependencies remain installed: {', '.join(summary['committed'])}")
            return False

        # Clean up completed savepoints
        self.savepoint_manager.cleanup_completed_savepoints()

        self.logger.info(f"✅ All addons installed successfully: {', '.join(successful_installs)}")
        return True

    def uninstall_addon(self, addon_name: str, force: bool = False) -> bool:
        """Uninstall a single addon"""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            raise ValueError(f"Addon {addon_name} not found")

        # Check if installed
        if addon_info.state != AddonState.INSTALLED:
            self.logger.info(f"Addon {addon_name} is not installed")
            return True

        # Check dependents
        dependents = self.dependency_manager.get_dependents(addon_name)
        if dependents and not force:
            installed_dependents = []
            for dependent in dependents:
                dep_info = self.state_manager.get_addon_info(dependent)
                if dep_info and dep_info.state == AddonState.INSTALLED:
                    installed_dependents.append(dependent)

            if installed_dependents:
                raise ValueError(f"Cannot uninstall {addon_name}: required by {', '.join(installed_dependents)}")

        # Uninstall dependents first if force
        if force:
            for dependent in dependents:
                dep_info = self.state_manager.get_addon_info(dependent)
                if dep_info and dep_info.state == AddonState.INSTALLED:
                    self.uninstall_addon(dependent, force=True)

        # Update state
        self.state_manager.mark_addon_uninstalled(addon_name)

        self.logger.info(f"Successfully uninstalled addon: {addon_name}")
        return True

    async def upgrade_addon(self, addon_name: str, force: bool = False) -> bool:
        """Upgrade a single addon"""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            raise ValueError(f"Addon {addon_name} not found")

        # Check if installed
        if addon_info.state != AddonState.INSTALLED:
            raise ValueError(f"Addon {addon_name} is not installed")

        # Check if upgrade is needed
        if addon_info.installed_version == addon_info.available_version and not force:
            self.logger.info(f"Addon {addon_name} is already up to date")
            return True

        # Uninstall current version
        if not self.uninstall_addon(addon_name, force=True):
            raise RuntimeError(f"Failed to uninstall current version of {addon_name}")

        # Install new version
        if not await self.install_addon(addon_name, force):
            raise RuntimeError(f"Failed to install new version of {addon_name}")

        self.logger.info(f"Upgraded addon: {addon_name}")
        return True

    def get_addon_info(self, addon_name: str) -> Optional[AddonInfo]:
        """Get detailed information about an addon"""
        return self.state_manager.get_addon_info(addon_name)

    def list_addons(self, state_filter: AddonState = None) -> Dict[str, AddonInfo]:
        """List addons, optionally filtered by state"""
        return self.state_manager.list_addons(state_filter)

    def get_dependency_tree(self, addon_name: str, max_depth: int = 10) -> dict:
        """Get dependency tree for an addon"""
        return self.dependency_manager.get_dependency_tree(addon_name, max_depth)

    def validate_addon_integrity(self, addon_name: str) -> Tuple[bool, List[str]]:
        """Validate addon integrity and configuration"""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            return False, [f"Addon {addon_name} not found"]

        errors = []

        # Check if addon directory exists
        if not os.path.exists(addon_info.manifest.addon_path):
            errors.append(f"Addon directory not found: {addon_info.manifest.addon_path}")

        # Check if manifest file exists
        manifest_file = os.path.join(addon_info.manifest.addon_path, '__manifest__.py')
        if not os.path.exists(manifest_file):
            errors.append(f"Manifest file not found: {manifest_file}")

        # Check if __init__.py exists
        init_file = os.path.join(addon_info.manifest.addon_path, '__init__.py')
        if not os.path.exists(init_file):
            errors.append(f"__init__.py file not found: {init_file}")

        # Check dependencies
        for dep in addon_info.dependencies:
            if not self.state_manager.get_addon_info(dep):
                errors.append(f"Dependency {dep} not available")

        return len(errors) == 0, errors
